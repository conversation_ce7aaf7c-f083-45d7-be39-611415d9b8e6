%% 辐射检测算法演示程序
% 复现C代码中的辐射检测算法，包括：
% 1. 计数率CPS到剂量率μSv/h的分段二次拟合转换
% 2. 3σ跳变检测算法
% 3. 连续增减趋势检测算法
% 4. 数据监测和报警功能

clear; clc; close all;

%% 1. 创建辐射检测系统对象
detector = RadiationDetectionSystem();

%% 2. 设置跳变检测参数（对应C代码中的drc_set_jpavg_param函数）
cgm1 = 3.0;              % 3σ精确阈值
cgm2 = 10.0;             % 3σ粗阈值  
out_cgm1_nt = 3;         % 连续超出精确阈值次数
continue_change_nt = 10; % 连续增减次数阈值
min_data_num = 10;       % 最小数据量

detector.set_jpavg_param(cgm1, cgm2, out_cgm1_nt, continue_change_nt, min_data_num);

%% 3. 设置分段拟合参数（对应C代码中system_setings.csi_dr_fitting_param）
% 这些参数来自C代码中的实际配置
fitting_params(1).cps_start = 0;
fitting_params(1).cps_end = 6.8;
fitting_params(1).param_a = 0;
fitting_params(1).param_b = 0.3235;
fitting_params(1).param_c = 0;

fitting_params(2).cps_start = 6.8;
fitting_params(2).cps_end = 21.7;
fitting_params(2).param_a = 0;
fitting_params(2).param_b = 0.442;
fitting_params(2).param_c = -0.8;

fitting_params(3).cps_start = 21.7;
fitting_params(3).cps_end = 92.0;
fitting_params(3).param_a = 0;
fitting_params(3).param_b = 0.4523;
fitting_params(3).param_c = -1.0159;

fitting_params(4).cps_start = 92.0;
fitting_params(4).cps_end = 186.6;
fitting_params(4).param_a = 0;
fitting_params(4).param_b = 0.463;
fitting_params(4).param_c = -1.996;

fitting_params(5).cps_start = 186.6;
fitting_params(5).cps_end = 634.0;
fitting_params(5).param_a = 0;
fitting_params(5).param_b = 0.303;
fitting_params(5).param_c = 27.8;

detector.set_fitting_param(fitting_params);

%% 4. 设置报警阈值（对应C代码中的报警参数）
max_dose_rate = 60.0;    % 最大剂量率报警阈值 μSv/h
min_dose_rate = 0;       % 最小剂量率报警阈值（0表示不报警）
max_dose_sum = 0;        % 累计剂量报警阈值（0表示不报警）

detector.set_alarm_thresholds(max_dose_rate, min_dose_rate, max_dose_sum);

%% 5. 开启传感器
detector.sensor_switch(1);

%% 6. 模拟数据采集和处理
fprintf('=== 辐射检测算法演示 ===\n\n');

% 模拟不同场景的计数数据
simulation_time = 200;  % 模拟200秒
cps_data = zeros(1, simulation_time);
dose_rate_data = zeros(1, simulation_time);
jump_flags = zeros(1, simulation_time);
alarm_flags = zeros(1, simulation_time);

% 场景1: 正常背景辐射 (0-50秒)
normal_background = 5 + 2*randn(1, 50);  % 均值5，标准差2的正态分布
normal_background(normal_background < 0) = 0;  % 确保非负

% 场景2: 辐射跳变 (51-100秒)
jump_level = 25 + 3*randn(1, 50);  % 跳变到更高水平
jump_level(jump_level < 0) = 0;

% 场景3: 缓慢增加趋势 (101-150秒)
trend_start = 8;
trend_data = trend_start + (1:50)*0.3 + 1*randn(1, 50);  % 线性增加趋势
trend_data(trend_data < 0) = 0;

% 场景4: 高辐射水平 (151-200秒)
high_level = 100 + 10*randn(1, 50);  % 高辐射水平
high_level(high_level < 0) = 0;

% 合并所有场景
simulated_counts = [normal_background, jump_level, trend_data, high_level];

fprintf('开始模拟数据处理...\n');
fprintf('场景说明：\n');
fprintf('  0-50秒: 正常背景辐射\n');
fprintf(' 51-100秒: 辐射跳变\n');
fprintf('101-150秒: 缓慢增加趋势\n');
fprintf('151-200秒: 高辐射水平\n\n');

%% 7. 逐秒处理数据（模拟秒中断）
for t = 1:simulation_time
    % 模拟脉冲计数（在实际系统中这是通过脉冲中断累计的）
    pulse_count = round(simulated_counts(t));
    for p = 1:pulse_count
        detector.pulse_interrupt_handler();
    end
    
    % 秒中断处理
    detector.second_interrupt_handler();
    
    % 记录数据
    cps_data(t) = detector.cps;
    dose_rate_data(t) = detector.dose_rate;
    jump_flags(t) = detector.jump_averager.jump_flag;
    alarm_flags(t) = detector.alarm;
    
    % 打印关键时刻的状态
    if t == 1 || t == 51 || t == 101 || t == 151 || mod(t, 20) == 0
        detector.print_status();
    end
    
    % 检测到跳变时打印详细信息
    if detector.jump_averager.jump_flag ~= 0
        fprintf('*** 跳变检测 *** ');
        detector.print_status();
    end
    
    % 检测到报警时打印信息
    if detector.alarm ~= 0
        fprintf('!!! 报警触发 !!! ');
        detector.print_status();
    end
end

%% 8. 结果可视化
figure('Position', [100, 100, 1200, 800]);

% 子图1: 原始计数数据和平滑后的CPS
subplot(2,2,1);
time_axis = 1:simulation_time;
plot(time_axis, simulated_counts, 'b-', 'LineWidth', 1, 'DisplayName', '原始计数');
hold on;
plot(time_axis, cps_data, 'r-', 'LineWidth', 2, 'DisplayName', '平滑CPS');
xlabel('时间 (秒)');
ylabel('计数率 (CPS)');
title('计数率数据处理');
legend('show');
grid on;

% 子图2: 剂量率转换结果
subplot(2,2,2);
plot(time_axis, dose_rate_data, 'g-', 'LineWidth', 2);
xlabel('时间 (秒)');
ylabel('剂量率 (μSv/h)');
title('CPS到剂量率转换');
grid on;

% 添加报警阈值线
if max_dose_rate > 0
    hold on;
    plot([1, simulation_time], [max_dose_rate, max_dose_rate], 'r--', 'LineWidth', 2, 'DisplayName', '报警阈值');
    legend('show');
end

% 子图3: 跳变检测结果
subplot(2,2,3);
jump_types = {'无跳变', '3σ粗阈值', '3σ精确阈值', '连续趋势'};
jump_colors = [0.8 0.8 0.8; 1 0.5 0; 1 0 0; 0.5 0 1];

for i = 0:3
    jump_indices = find(jump_flags == i);
    if ~isempty(jump_indices)
        scatter(jump_indices, i*ones(size(jump_indices)), 50, jump_colors(i+1,:), 'filled');
        hold on;
    end
end

xlabel('时间 (秒)');
ylabel('跳变类型');
title('跳变检测结果');
yticks(0:3);
yticklabels(jump_types);
grid on;
ylim([-0.5, 3.5]);

% 子图4: 报警状态
subplot(2,2,4);
alarm_bits = [bitand(alarm_flags, 1), bitand(alarm_flags, 2)/2, bitand(alarm_flags, 4)/4];
plot(time_axis, alarm_bits(:,1), 'b-', 'LineWidth', 2, 'DisplayName', '剂量率过低');
hold on;
plot(time_axis, alarm_bits(:,2), 'r-', 'LineWidth', 2, 'DisplayName', '剂量率过高');
plot(time_axis, alarm_bits(:,3), 'm-', 'LineWidth', 2, 'DisplayName', '累计剂量过高');
xlabel('时间 (秒)');
ylabel('报警状态');
title('报警监测');
legend('show');
grid on;

sgtitle('辐射检测算法完整演示', 'FontSize', 16, 'FontWeight', 'bold');

%% 9. 统计分析
fprintf('\n=== 算法性能统计 ===\n');
fprintf('总处理时间: %d 秒\n', simulation_time);
fprintf('检测到的跳变次数: %d\n', sum(jump_flags > 0));
fprintf('3σ粗阈值跳变: %d 次\n', sum(jump_flags == 1));
fprintf('3σ精确阈值跳变: %d 次\n', sum(jump_flags == 2));
fprintf('连续趋势跳变: %d 次\n', sum(jump_flags == 3));
fprintf('触发报警次数: %d\n', sum(alarm_flags > 0));
fprintf('最终累计剂量: %.6f μSv\n', detector.dose_sum);
fprintf('平均剂量率: %.4f μSv/h\n', mean(dose_rate_data(dose_rate_data > 0)));

%% 10. 展示分段拟合曲线
figure('Position', [200, 200, 800, 600]);
cps_range = 0:1:650;
dose_rate_fitted = zeros(size(cps_range));

for i = 1:length(cps_range)
    x = cps_range(i);
    % 找到对应的拟合区间
    param_index = 1;
    for j = 1:length(fitting_params)
        if x >= fitting_params(j).cps_start && x <= fitting_params(j).cps_end
            param_index = j;
            break;
        end
    end
    if param_index == 0
        param_index = length(fitting_params);
    end
    
    % 计算拟合值
    a = fitting_params(param_index).param_a;
    b = fitting_params(param_index).param_b;
    c = fitting_params(param_index).param_c;
    dose_rate_fitted(i) = x*x*a + x*b + c;
end

plot(cps_range, dose_rate_fitted, 'b-', 'LineWidth', 2);
hold on;

% 标记分段边界
for i = 1:length(fitting_params)
    xline(fitting_params(i).cps_start, 'r--', 'Alpha', 0.7);
    if i == length(fitting_params)
        xline(fitting_params(i).cps_end, 'r--', 'Alpha', 0.7);
    end
end

% 标记实际数据点
scatter(cps_data, dose_rate_data, 20, 'r', 'filled', 'Alpha', 0.6);

xlabel('计数率 CPS');
ylabel('剂量率 (μSv/h)');
title('分段二次拟合曲线：CPS → 剂量率转换');
grid on;
legend('拟合曲线', '分段边界', '实际数据点', 'Location', 'northwest');

fprintf('\n=== 演示完成 ===\n');
fprintf('已生成可视化图表，展示了完整的辐射检测算法流程。\n');
