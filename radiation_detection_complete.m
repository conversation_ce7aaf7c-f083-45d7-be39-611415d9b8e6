%% 辐射检测算法完整复现 - 单文件版本
% 复现C代码中的辐射检测算法，包括：
% 1. 计数率CPS到剂量率μSv/h的分段二次拟合转换
% 2. 3σ跳变检测算法  
% 3. 连续增减趋势检测算法
% 4. 数据监测和报警功能

clear; clc; close all;

%% ========== 算法参数设置 ==========
% 跳变检测参数
cgm1 = 3.0;              % 3σ精确阈值
cgm2 = 10.0;             % 3σ粗阈值
out_cgm1_nt = 3;         % 连续超出精确阈值次数
continue_change_nt = 10; % 连续增减次数阈值
min_data_num = 10;       % 最小数据量

% 分段拟合参数（来自C代码实际配置）
fitting_params = [
    0,     6.8,   0,      0.3235,  0;
    6.8,   21.7,  0,      0.442,   -0.8;
    21.7,  92.0,  0,      0.4523,  -1.0159;
    92.0,  186.6, 0,      0.463,   -1.996;
    186.6, 634.0, 0,      0.303,   27.8
];
% 列：[cps_start, cps_end, param_a, param_b, param_c]

% 报警阈值
max_dose_rate = 60.0;    % 最大剂量率报警阈值 μSv/h
min_dose_rate = 0;       % 最小剂量率报警阈值
max_dose_sum = 0;        % 累计剂量报警阈值

%% ========== 初始化变量 ==========
% 跳变检测器状态
DATA_BUF_LEN = 100;
LAST_DATA_BUF_LEN = 60;

% 主数据缓存
data_buffer = zeros(1, DATA_BUF_LEN);
data_buffer_index = 1;
data_buffer_filed_num = 0;
data_buffer_sum = 0;

% 最近数据缓存
last_data_buffer = zeros(1, LAST_DATA_BUF_LEN);
last_data_buffer_index = 1;
last_data_buffer_filed_num = 0;

% 方差缓存
data_variance_buffer = zeros(1, DATA_BUF_LEN);
data_variance_buffer_index = 1;
data_variance_buffer_filed_num = 0;
data_variance_buffer_sum = 0;

% 稳定数据统计
stable_data_average = 0;
stable_data_average_variance = 0;
recent_average_buffer = zeros(1, LAST_DATA_BUF_LEN);
recent_average_buffer_index = 1;

% 跳变检测计数器
out_difference_times = 0;
increase_times = 0;
decrease_times = 0;
jump_flag = 0;
last_data_average = 0;

% 传感器状态
power_status = true;
re_start = true;
cnt_temp = 0;
cnt_now = 0;
cps = 0;
dose_rate = 0;
dose_sum = 0;
alarm = 0;
current_time = 0;

%% ========== 模拟数据生成 ==========
simulation_time = 200;
fprintf('=== 辐射检测算法演示 ===\n\n');

% 生成不同场景的模拟数据
normal_background = 5 + 2*randn(1, 50);     % 正常背景辐射
jump_level = 25 + 3*randn(1, 50);           % 辐射跳变
trend_start = 8;
trend_data = trend_start + (1:50)*0.3 + 1*randn(1, 50);  % 缓慢增加趋势
high_level = 100 + 10*randn(1, 50);         % 高辐射水平

% 确保非负
normal_background(normal_background < 0) = 0;
jump_level(jump_level < 0) = 0;
trend_data(trend_data < 0) = 0;
high_level(high_level < 0) = 0;

simulated_counts = [normal_background, jump_level, trend_data, high_level];

% 结果存储数组
cps_data = zeros(1, simulation_time);
dose_rate_data = zeros(1, simulation_time);
jump_flags = zeros(1, simulation_time);
alarm_flags = zeros(1, simulation_time);
raw_counts = zeros(1, simulation_time);

fprintf('场景说明：\n');
fprintf('  0-50秒: 正常背景辐射 (~5 CPS)\n');
fprintf(' 51-100秒: 辐射跳变 (~25 CPS)\n');
fprintf('101-150秒: 缓慢增加趋势 (8→23 CPS)\n');
fprintf('151-200秒: 高辐射水平 (~100 CPS)\n\n');

%% ========== 主处理循环 ==========
for t = 1:simulation_time
    current_time = t;
    
    % 模拟脉冲计数
    pulse_count = round(simulated_counts(t));
    cnt_temp = pulse_count;  % 模拟脉冲中断累计
    raw_counts(t) = pulse_count;
    
    % ===== 秒中断处理函数 =====
    cnt_now = cnt_temp;
    cnt_temp = 0;
    
    if power_status
        if re_start
            re_start = false;
            if t == 1 || any(t == [51, 101, 151])
                fprintf('时间 %d: 重启，丢弃第一秒数据\n', t);
            end
        else
            % ===== 跳变检测算法 =====
            data = cnt_now;
            jump_flag = 0;
            
            % 检查是否有足够的最近数据
            if last_data_buffer_filed_num >= out_cgm1_nt
                % 将最近数组中最老的数填入主数据缓存
                save_data = last_data_buffer(last_data_buffer_index);
                
                % 更新主数据缓存（环形缓存）
                data_buffer_sum = data_buffer_sum - data_buffer(data_buffer_index);
                data_buffer(data_buffer_index) = save_data;
                data_buffer_sum = data_buffer_sum + save_data;
                
                data_buffer_index = data_buffer_index + 1;
                if data_buffer_index > DATA_BUF_LEN
                    data_buffer_index = 1;
                end
                
                if data_buffer_filed_num < DATA_BUF_LEN
                    data_buffer_filed_num = data_buffer_filed_num + 1;
                end
                
                % 计算当前平均值
                stable_data_average = data_buffer_sum / data_buffer_filed_num;
                
                % 计算方差
                if data_buffer_filed_num >= 2
                    variance_sum = 0;
                    for i = 1:data_buffer_filed_num
                        diff = data_buffer(i) - stable_data_average;
                        variance_sum = variance_sum + diff * diff;
                    end
                    current_variance = variance_sum / data_buffer_filed_num;
                    
                    % 更新方差缓存
                    data_variance_buffer_sum = data_variance_buffer_sum - data_variance_buffer(data_variance_buffer_index);
                    data_variance_buffer(data_variance_buffer_index) = current_variance;
                    data_variance_buffer_sum = data_variance_buffer_sum + current_variance;
                    
                    data_variance_buffer_index = data_variance_buffer_index + 1;
                    if data_variance_buffer_index > DATA_BUF_LEN
                        data_variance_buffer_index = 1;
                    end
                    
                    if data_variance_buffer_filed_num < DATA_BUF_LEN
                        data_variance_buffer_filed_num = data_variance_buffer_filed_num + 1;
                    end
                    
                    avg_variance = data_variance_buffer_sum / data_variance_buffer_filed_num;
                    stable_data_average_variance = sqrt(avg_variance);
                end
                
                % 跳变检测算法
                if data_buffer_filed_num >= min_data_num
                    difference = abs(data - stable_data_average);
                    n_cgm_th_1 = cgm1 * stable_data_average_variance;
                    n_cgm_th_2 = cgm2 * stable_data_average_variance;
                    
                    if difference == 0
                        out_difference_times = 0;
                    elseif difference > n_cgm_th_2
                        % 超过粗阈值，立即判定为跳变
                        jump_flag = 1;
                        stable_data_average = recent_average_buffer(recent_average_buffer_index);
                        recent_average_buffer = zeros(1, LAST_DATA_BUF_LEN);
                        recent_average_buffer_index = 1;
                    elseif difference > n_cgm_th_1
                        % 超过精确阈值，累计次数
                        out_difference_times = out_difference_times + 1;
                        if out_difference_times >= out_cgm1_nt
                            jump_flag = 2;
                            stable_data_average = recent_average_buffer(recent_average_buffer_index);
                            recent_average_buffer = zeros(1, LAST_DATA_BUF_LEN);
                            recent_average_buffer_index = 1;
                        end
                    else
                        out_difference_times = 0;
                    end
                    
                    % 连续增减趋势检测
                    if (stable_data_average - last_data_average) > 0.001
                        decrease_times = 0;
                        increase_times = increase_times + 1;
                    elseif (stable_data_average - last_data_average) < -0.001
                        increase_times = 0;
                        decrease_times = decrease_times + 1;
                    else
                        increase_times = 0;
                        decrease_times = 0;
                    end
                    
                    if increase_times >= continue_change_nt || decrease_times >= continue_change_nt
                        jump_flag = 3;
                        stable_data_average = recent_average_buffer(recent_average_buffer_index);
                        recent_average_buffer = zeros(1, LAST_DATA_BUF_LEN);
                        recent_average_buffer_index = 1;
                    end
                end
                
                % 更新历史平均值记录
                last_data_average = stable_data_average;
                recent_average_buffer(recent_average_buffer_index) = last_data_average;
                recent_average_buffer_index = recent_average_buffer_index + 1;
                max_recent_len = min_data_num + continue_change_nt;
                if recent_average_buffer_index > max_recent_len
                    recent_average_buffer_index = 1;
                end
            end
            
            % 填入本次数据到最近数组
            last_data_buffer(last_data_buffer_index) = data;
            last_data_buffer_index = last_data_buffer_index + 1;
            if last_data_buffer_index > out_cgm1_nt
                last_data_buffer_index = 1;
            end
            
            if last_data_buffer_filed_num < out_cgm1_nt
                last_data_buffer_filed_num = last_data_buffer_filed_num + 1;
            end
            
            % 获取CPS
            cps = stable_data_average;
            
            % 处理跳变
            if jump_flag ~= 0
                fprintf('时间 %d: 检测到跳变 (类型: %d), 当前CPS: %.2f\n', t, jump_flag, cps);
                re_start = true;
                % 清空缓存
                data_buffer = zeros(1, DATA_BUF_LEN);
                data_buffer_index = 1;
                data_buffer_filed_num = 0;
                data_buffer_sum = 0;
                last_data_buffer = zeros(1, LAST_DATA_BUF_LEN);
                last_data_buffer_index = 1;
                last_data_buffer_filed_num = 0;
                data_variance_buffer = zeros(1, DATA_BUF_LEN);
                data_variance_buffer_index = 1;
                data_variance_buffer_filed_num = 0;
                data_variance_buffer_sum = 0;
                stable_data_average = 0;
                stable_data_average_variance = 0;
                recent_average_buffer = zeros(1, LAST_DATA_BUF_LEN);
                recent_average_buffer_index = 1;
                out_difference_times = 0;
                last_data_average = 0;
                increase_times = 0;
                decrease_times = 0;
            else
                % ===== 剂量率计算 =====
                x = cps;
                param_index = size(fitting_params, 1);  % 默认使用最后一个区间
                
                for i = 1:size(fitting_params, 1)
                    if x >= fitting_params(i, 1) && x <= fitting_params(i, 2)
                        param_index = i;
                        break;
                    end
                end
                
                a = fitting_params(param_index, 3);
                b = fitting_params(param_index, 4);
                c = fitting_params(param_index, 5);
                dose_rate = x * x * a + x * b + c;
                
                % ===== 报警检查 =====
                alarm = 0;
                if max_dose_rate > 0 && dose_rate >= max_dose_rate
                    alarm = bitor(alarm, 2);  % bit1报警
                end
                if min_dose_rate > 0 && dose_rate <= min_dose_rate
                    alarm = bitor(alarm, 1);  % bit0报警
                end
                
                % 累计剂量（只有在稳定时才累计）
                if data_variance_buffer_filed_num > min_data_num
                    dose_sum = dose_sum + dose_rate / 3600;
                    if max_dose_sum > 0 && dose_sum >= max_dose_sum
                        alarm = bitor(alarm, 4);  % bit2报警
                    end
                end
                
                % 打印关键时刻状态
                if t == 1 || any(t == [51, 101, 151]) || mod(t, 40) == 0
                    fprintf('时间: %d, CPS: %.2f, 剂量率: %.4f μSv/h, 累计: %.6f μSv, 报警: %d\n', ...
                        t, cps, dose_rate, dose_sum, alarm);
                end
                
                if alarm ~= 0
                    fprintf('!!! 报警触发 !!! 时间: %d, 剂量率: %.4f μSv/h\n', t, dose_rate);
                end
            end
        end
    end
    
    % 记录数据
    cps_data(t) = cps;
    dose_rate_data(t) = dose_rate;
    jump_flags(t) = jump_flag;
    alarm_flags(t) = alarm;
end

%% ========== 结果可视化 ==========
fprintf('\n=== 算法性能统计 ===\n');
fprintf('总处理时间: %d 秒\n', simulation_time);
fprintf('检测到的跳变次数: %d\n', sum(jump_flags > 0));
fprintf('3σ粗阈值跳变: %d 次\n', sum(jump_flags == 1));
fprintf('3σ精确阈值跳变: %d 次\n', sum(jump_flags == 2));
fprintf('连续趋势跳变: %d 次\n', sum(jump_flags == 3));
fprintf('触发报警次数: %d\n', sum(alarm_flags > 0));
fprintf('最终累计剂量: %.6f μSv\n', dose_sum);
fprintf('平均剂量率: %.4f μSv/h\n', mean(dose_rate_data(dose_rate_data > 0)));

% 创建主要结果图
figure('Position', [100, 100, 1400, 900]);

% 子图1: 原始计数数据和平滑后的CPS
subplot(2,3,1);
time_axis = 1:simulation_time;
plot(time_axis, raw_counts, 'b-', 'LineWidth', 1, 'DisplayName', '原始计数');
hold on;
plot(time_axis, cps_data, 'r-', 'LineWidth', 2, 'DisplayName', '平滑CPS');
xlabel('时间 (秒)');
ylabel('计数率 (CPS)');
title('计数率数据处理');
legend('show');
grid on;

% 添加场景分界线
line([50.5, 50.5], ylim, 'Color', 'k', 'LineStyle', '--', 'LineWidth', 1);
line([100.5, 100.5], ylim, 'Color', 'k', 'LineStyle', '--', 'LineWidth', 1);
line([150.5, 150.5], ylim, 'Color', 'k', 'LineStyle', '--', 'LineWidth', 1);
text(25, max(raw_counts)*0.9, '正常背景', 'HorizontalAlignment', 'center');
text(75, max(raw_counts)*0.9, '辐射跳变', 'HorizontalAlignment', 'center');
text(125, max(raw_counts)*0.9, '缓慢增加', 'HorizontalAlignment', 'center');
text(175, max(raw_counts)*0.9, '高辐射', 'HorizontalAlignment', 'center');

% 子图2: 剂量率转换结果
subplot(2,3,2);
plot(time_axis, dose_rate_data, 'g-', 'LineWidth', 2);
xlabel('时间 (秒)');
ylabel('剂量率 (μSv/h)');
title('CPS到剂量率转换');
grid on;

% 添加报警阈值线
if max_dose_rate > 0
    hold on;
    plot([1, simulation_time], [max_dose_rate, max_dose_rate], 'r--', 'LineWidth', 2, 'DisplayName', '报警阈值');
    legend('剂量率', '报警阈值', 'Location', 'best');
end

% 子图3: 跳变检测结果
subplot(2,3,3);
jump_types = {'无跳变', '3σ粗阈值', '3σ精确阈值', '连续趋势'};
jump_colors = [0.8 0.8 0.8; 1 0.5 0; 1 0 0; 0.5 0 1];

for i = 0:3
    jump_indices = find(jump_flags == i);
    if ~isempty(jump_indices)
        scatter(jump_indices, i*ones(size(jump_indices)), 50, jump_colors(i+1,:), 'filled');
        hold on;
    end
end

xlabel('时间 (秒)');
ylabel('跳变类型');
title('跳变检测结果');
yticks(0:3);
yticklabels(jump_types);
grid on;
ylim([-0.5, 3.5]);

% 子图4: 报警状态
subplot(2,3,4);
alarm_bits = [bitand(alarm_flags, 1), bitand(alarm_flags, 2)/2, bitand(alarm_flags, 4)/4];
plot(time_axis, alarm_bits(:,1), 'b-', 'LineWidth', 2, 'DisplayName', '剂量率过低');
hold on;
plot(time_axis, alarm_bits(:,2), 'r-', 'LineWidth', 2, 'DisplayName', '剂量率过高');
plot(time_axis, alarm_bits(:,3), 'm-', 'LineWidth', 2, 'DisplayName', '累计剂量过高');
xlabel('时间 (秒)');
ylabel('报警状态');
title('报警监测');
legend('show', 'Location', 'best');
grid on;

% 子图5: 累计剂量
subplot(2,3,5);
dose_sum_array = cumsum(dose_rate_data) / 3600;  % 重新计算用于显示
plot(time_axis, dose_sum_array, 'm-', 'LineWidth', 2);
xlabel('时间 (秒)');
ylabel('累计剂量 (μSv)');
title('累计剂量变化');
grid on;

% 子图6: 3σ检测详情
subplot(2,3,6);
% 显示最后50个数据点的3σ检测情况
if length(cps_data) >= 50
    recent_data = cps_data(end-49:end);
    recent_time = time_axis(end-49:end);
    recent_avg = mean(recent_data);
    recent_std = std(recent_data);

    plot(recent_time, recent_data, 'b-', 'LineWidth', 1.5);
    hold on;
    plot(recent_time, recent_avg * ones(size(recent_time)), 'g-', 'LineWidth', 2, 'DisplayName', '均值');
    plot(recent_time, (recent_avg + cgm1*recent_std) * ones(size(recent_time)), 'r--', 'LineWidth', 1, 'DisplayName', '3σ精确阈值');
    plot(recent_time, (recent_avg + cgm2*recent_std) * ones(size(recent_time)), 'r-', 'LineWidth', 2, 'DisplayName', '3σ粗阈值');
    plot(recent_time, (recent_avg - cgm1*recent_std) * ones(size(recent_time)), 'r--', 'LineWidth', 1);
    plot(recent_time, (recent_avg - cgm2*recent_std) * ones(size(recent_time)), 'r-', 'LineWidth', 2);
end
xlabel('时间 (秒)');
ylabel('CPS');
title('3σ检测阈值 (最后50秒)');
legend('show', 'Location', 'best');
grid on;

sgtitle('辐射检测算法完整演示 - C代码MATLAB复现', 'FontSize', 16, 'FontWeight', 'bold');

% 创建分段拟合曲线图
figure('Position', [200, 200, 1000, 700]);

% 子图1: 分段拟合曲线
subplot(2,1,1);
cps_range = 0:1:650;
dose_rate_fitted = zeros(size(cps_range));

for i = 1:length(cps_range)
    x = cps_range(i);
    param_index = size(fitting_params, 1);
    for j = 1:size(fitting_params, 1)
        if x >= fitting_params(j, 1) && x <= fitting_params(j, 2)
            param_index = j;
            break;
        end
    end

    a = fitting_params(param_index, 3);
    b = fitting_params(param_index, 4);
    c = fitting_params(param_index, 5);
    dose_rate_fitted(i) = x*x*a + x*b + c;
end

plot(cps_range, dose_rate_fitted, 'b-', 'LineWidth', 3);
hold on;

% 标记分段边界
colors = 'rgmcy';
for i = 1:size(fitting_params, 1)
    line([fitting_params(i, 1), fitting_params(i, 1)], ylim, 'Color', colors(mod(i-1,5)+1), 'LineStyle', '--', 'LineWidth', 2);
    if i == size(fitting_params, 1)
        line([fitting_params(i, 2), fitting_params(i, 2)], ylim, 'Color', colors(mod(i-1,5)+1), 'LineStyle', '--', 'LineWidth', 2);
    end

    % 添加区间标签
    mid_x = (fitting_params(i, 1) + fitting_params(i, 2)) / 2;
    mid_y = mid_x*mid_x*fitting_params(i, 3) + mid_x*fitting_params(i, 4) + fitting_params(i, 5);
    text(mid_x, mid_y, sprintf('区间%d', i), 'HorizontalAlignment', 'center', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black', 'FontSize', 8);
end

% 标记实际数据点
scatter(cps_data, dose_rate_data, 30, 'r', 'filled');

xlabel('计数率 CPS');
ylabel('剂量率 (μSv/h)');
title('分段二次拟合曲线：CPS → 剂量率转换');
grid on;
legend('拟合曲线', '分段边界', '实际数据点', 'Location', 'northwest');

% 子图2: 拟合参数表
subplot(2,1,2);
axis off;
param_table = cell(size(fitting_params, 1)+1, 6);
param_table(1, :) = {'区间', 'CPS起始', 'CPS结束', '参数a', '参数b', '参数c'};
for i = 1:size(fitting_params, 1)
    param_table(i+1, :) = {sprintf('区间%d', i), ...
                          sprintf('%.1f', fitting_params(i, 1)), ...
                          sprintf('%.1f', fitting_params(i, 2)), ...
                          sprintf('%.6f', fitting_params(i, 3)), ...
                          sprintf('%.4f', fitting_params(i, 4)), ...
                          sprintf('%.4f', fitting_params(i, 5))};
end

% 创建表格
t = uitable('Data', param_table(2:end, :), 'ColumnName', param_table(1, :), ...
           'Position', [50, 50, 900, 150], 'FontSize', 10);

text(0.5, 0.8, '分段拟合参数表 (剂量率 = a×CPS² + b×CPS + c)', ...
     'HorizontalAlignment', 'center', 'FontSize', 14, 'FontWeight', 'bold');

fprintf('\n=== 分段拟合参数 ===\n');
fprintf('区间\tCPS范围\t\t参数a\t\t参数b\t\t参数c\n');
for i = 1:size(fitting_params, 1)
    fprintf('%d\t%.1f-%.1f\t\t%.6f\t%.4f\t\t%.4f\n', i, ...
            fitting_params(i, 1), fitting_params(i, 2), ...
            fitting_params(i, 3), fitting_params(i, 4), fitting_params(i, 5));
end

fprintf('\n=== 演示完成 ===\n');
fprintf('已完整复现C代码中的辐射检测算法，包括：\n');
fprintf('1. 3σ跳变检测算法（精确阈值和粗阈值）\n');
fprintf('2. 连续增减趋势检测算法\n');
fprintf('3. 分段二次拟合CPS到剂量率转换\n');
fprintf('4. 实时数据监测和报警功能\n');
fprintf('5. 完整的可视化展示\n');
