classdef RadiationDetectionSystem < handle
    % 辐射检测系统 - MATLAB复现版本
    % 复现C代码中的辐射检测算法，包括跳变检测、3sigma检测、趋势检测等
    
    properties
        % 跳变检测器参数
        jump_averager
        
        % 传感器状态
        power_status = false
        re_start = false
        cnt_temp = 0
        cnt_now = 0
        
        % 拟合参数
        fitting_param = []
        fitting_param_num = 0
        
        % 报警阈值
        alarm_param_max_dose_rate = 0
        alarm_param_min_dose_rate = 0
        alarm_param_max_dose_sum = 0
        
        % 结果数据
        cps = 0              % 计数率
        dose_rate = 0        % 剂量率
        dose_sum = 0         % 总剂量
        alarm = 0            % 报警标志
        
        % 时间记录
        current_time = 0
    end
    
    methods
        function obj = RadiationDetectionSystem()
            % 构造函数 - 初始化跳变检测器
            obj.jump_averager = JumpAverager();
        end
        
        function sensor_switch(obj, switch_state)
            % 开关采集：开关电源、开关秒中断
            % switch_state: 0关闭，1开启
            obj.power_status = logical(switch_state);
            obj.re_start = logical(switch_state);
            if switch_state
                fprintf('传感器已开启\n');
            else
                fprintf('传感器已关闭\n');
            end
        end
        
        function set_jpavg_param(obj, cgm1, cgm2, out_cgm1_nt, continue_change_nt, min_data_num)
            % 设置跳变检测平均器参数
            % cgm1: nσ检测跳变法，σ阈值1,精确阈值(较窄) 如：3.0
            % cgm2: nσ检测跳变法，σ阈值2,粗阈值(较宽) 如：10.0
            % out_cgm1_nt: 连续处于σ阈值1以外的次数 如：3
            % continue_change_nt: 连续增减次数 如：10
            % min_data_num: 跳变检测算法运行的最少数据量 如：10
            
            obj.jump_averager.param_cgm_1 = cgm1;
            obj.jump_averager.param_out_cgm_1_t = out_cgm1_nt;
            obj.jump_averager.param_cgm_2 = cgm2;
            obj.jump_averager.param_ct_inodc_t = continue_change_nt;
            obj.jump_averager.jump_check_start_data_num = min_data_num;
        end
        
        function set_fitting_param(obj, fitting_params)
            % 设置分段拟合参数
            % fitting_params: 结构体数组，包含cps_start, cps_end, param_a, param_b, param_c
            obj.fitting_param = fitting_params;
            obj.fitting_param_num = length(fitting_params);
        end
        
        function set_alarm_thresholds(obj, max_dose_rate, min_dose_rate, max_dose_sum)
            % 设置报警阈值
            obj.alarm_param_max_dose_rate = max_dose_rate;
            obj.alarm_param_min_dose_rate = min_dose_rate;
            obj.alarm_param_max_dose_sum = max_dose_sum;
        end
        
        function pulse_interrupt_handler(obj)
            % 脉冲中断处理函数 - 累计计数
            obj.cnt_temp = obj.cnt_temp + 1;
        end
        
        function second_interrupt_handler(obj)
            % 秒中断处理函数 - 主要算法逻辑
            obj.current_time = obj.current_time + 1;
            obj.cnt_now = obj.cnt_temp;  % 获取当前脉冲计数值
            obj.cnt_temp = 0;            % 清零，得到每秒计数值cps
            
            if obj.power_status
                if obj.re_start
                    % 丢弃第一秒数据，重新开始标志
                    obj.re_start = false;
                    fprintf('时间 %d: 重启，丢弃第一秒数据\n', obj.current_time);
                else
                    % 跳变检测
                    pulse_jump_flag = obj.jump_averager.add_data_to_buffer(obj.cnt_now);
                    obj.cps = obj.jump_averager.get_data_stable_average();
                    
                    if pulse_jump_flag ~= 0
                        % 有跳变
                        fprintf('时间 %d: 检测到跳变 (类型: %d), 当前CPS: %.2f\n', ...
                            obj.current_time, pulse_jump_flag, obj.cps);
                        obj.re_start = true;
                        obj.jump_averager.data_buffer_clean();
                    else
                        % 无跳变，进行剂量率计算
                        obj.calculate_dose_rate();
                        obj.check_alarms();
                        
                        % 累计剂量（只有在稳定时才累计）
                        if obj.jump_averager.get_stable_filed_num() > obj.jump_averager.jump_check_start_data_num
                            obj.dose_sum = obj.dose_sum + obj.dose_rate / 3600;  % 转换为每秒剂量
                            
                            % 检查累计剂量报警
                            if obj.alarm_param_max_dose_sum > 0 && obj.dose_sum >= obj.alarm_param_max_dose_sum
                                obj.alarm = bitor(obj.alarm, 4);  % bit2报警
                            end
                        end
                    end
                end
            end
        end
        
        function calculate_dose_rate(obj)
            % 计算剂量率 - 分段二次拟合
            if obj.fitting_param_num == 0
                return;
            end
            
            x = obj.cps;  % 均值
            index = 0;
            
            % 找到匹配的区间
            for i = 1:obj.fitting_param_num
                if x >= obj.fitting_param(i).cps_start && x <= obj.fitting_param(i).cps_end
                    index = i;
                    break;
                end
            end
            
            % 如果超出所有区间范围，使用最后一个区间
            if index == 0
                index = obj.fitting_param_num;
            end
            
            % 二次拟合：dose_rate = a*x^2 + b*x + c
            a = obj.fitting_param(index).param_a;
            b = obj.fitting_param(index).param_b;
            c = obj.fitting_param(index).param_c;
            
            obj.dose_rate = x * x * a + x * b + c;
        end
        
        function check_alarms(obj)
            % 检查报警条件
            obj.alarm = 0;
            
            % 剂量率过高报警
            if obj.alarm_param_max_dose_rate > 0 && obj.dose_rate >= obj.alarm_param_max_dose_rate
                obj.alarm = bitor(obj.alarm, 2);  % bit1报警
            end
            
            % 剂量率过低报警
            if obj.alarm_param_min_dose_rate > 0 && obj.dose_rate <= obj.alarm_param_min_dose_rate
                obj.alarm = bitor(obj.alarm, 1);  % bit0报警
            end
        end
        
        function print_status(obj)
            % 打印当前状态
            fprintf('时间: %d, CPS: %.2f, 剂量率: %.4f μSv/h, 累计剂量: %.6f μSv, 报警: %d\n', ...
                obj.current_time, obj.cps, obj.dose_rate, obj.dose_sum, obj.alarm);
        end
    end
end
