classdef JumpAverager < handle
    % 跳变检测平均器类
    % 实现3σ检测法和连续增减趋势检测法
    
    properties (Constant)
        DATA_BUF_LEN = 100      % 原始数据缓存个数
        LAST_DATA_BUF_LEN = 60  % 最近数据缓存个数
    end
    
    properties
        % 主数据缓存
        data_buffer
        data_buffer_index = 1
        data_buffer_filed_num = 0
        data_buffer_sum = 0
        
        % 最近数据缓存
        last_data_buffer
        last_data_buffer_index = 1
        last_data_buffer_filed_num = 0
        
        % 方差缓存
        data_variance_buffer
        data_variance_buffer_index = 1
        data_variance_buffer_filed_num = 0
        data_variance_buffer_sum = 0
        
        % 稳定数据统计
        stable_data_average = 0
        stable_data_average_variance = 0
        recent_average_buffer
        recent_average_buffer_index = 1
        
        % 跳变检测计数器
        out_difference_times = 0    % 数据连续落于正态分布阈值以外的次数
        increase_times = 0          % 连续上升的次数
        decrease_times = 0          % 连续下降的次数
        
        % 跳变标志和历史数据
        jump_flag = 0
        last_data_average = 0
        
        % 算法参数
        jump_check_start_data_num = 10  % 跳变检测算法运行的最少数据量
        param_cgm_1 = 3.0              % nσ检测跳变法，σ阈值1,精确阈值(较窄)
        param_out_cgm_1_t = 3          % 连续处于σ阈值1以外的次数
        param_cgm_2 = 10.0             % nσ检测跳变法，σ阈值2,粗阈值(较宽)
        param_ct_inodc_t = 10          % 连续增、减次数，超过次数认为跳变
    end
    
    methods
        function obj = JumpAverager()
            % 构造函数 - 初始化缓存数组
            obj.data_buffer = zeros(1, obj.DATA_BUF_LEN);
            obj.last_data_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
            obj.data_variance_buffer = zeros(1, obj.DATA_BUF_LEN);
            obj.recent_average_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
        end
        
        function jump_flag = add_data_to_buffer(obj, data)
            % 添加原始数据到缓存并进行跳变检测
            % 返回值：0表示无跳变，1表示3σ方法宽阈值检测到跳变，
            %        2表示3σ方法窄阈值检测到跳变，3表示连续增减法检测到跳变
            
            jump_flag = 0;
            obj.jump_flag = 0;
            
            % 检查是否有足够的最近数据来进行处理
            if obj.last_data_buffer_filed_num >= obj.param_out_cgm_1_t
                % 将"最近数组"中最老的数填入原始数据缓存
                save_data = obj.last_data_buffer(obj.last_data_buffer_index);
                
                % 更新主数据缓存（环形缓存）
                obj.data_buffer_sum = obj.data_buffer_sum - obj.data_buffer(obj.data_buffer_index);
                obj.data_buffer(obj.data_buffer_index) = save_data;
                obj.data_buffer_sum = obj.data_buffer_sum + save_data;
                
                obj.data_buffer_index = obj.data_buffer_index + 1;
                if obj.data_buffer_index > obj.DATA_BUF_LEN
                    obj.data_buffer_index = 1;
                end
                
                if obj.data_buffer_filed_num < obj.DATA_BUF_LEN
                    obj.data_buffer_filed_num = obj.data_buffer_filed_num + 1;
                end
                
                % 计算当前平均值
                obj.stable_data_average = obj.data_buffer_sum / obj.data_buffer_filed_num;
                
                % 计算方差
                obj.calculate_variance();
                
                % 跳变检测算法
                if obj.data_buffer_filed_num >= obj.jump_check_start_data_num
                    difference = abs(data - obj.stable_data_average);
                    n_cgm_th_1 = obj.param_cgm_1 * obj.stable_data_average_variance;  % 3σ阈值1
                    n_cgm_th_2 = obj.param_cgm_2 * obj.stable_data_average_variance;  % 3σ阈值2
                    
                    if difference == 0
                        obj.out_difference_times = 0;
                    elseif difference > n_cgm_th_2
                        % 超过粗阈值，立即判定为跳变
                        obj.jump_flag = 1;
                        obj.stable_data_average = obj.recent_average_buffer(obj.recent_average_buffer_index);
                        obj.recent_average_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
                        obj.recent_average_buffer_index = 1;
                    elseif difference > n_cgm_th_1
                        % 超过精确阈值，累计次数
                        obj.out_difference_times = obj.out_difference_times + 1;
                        if obj.out_difference_times >= obj.param_out_cgm_1_t
                            obj.jump_flag = 2;
                            obj.stable_data_average = obj.recent_average_buffer(obj.recent_average_buffer_index);
                            obj.recent_average_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
                            obj.recent_average_buffer_index = 1;
                        end
                    else
                        obj.out_difference_times = 0;
                    end
                    
                    % 连续增减趋势检测
                    if (obj.stable_data_average - obj.last_data_average) > 0.001
                        obj.decrease_times = 0;
                        obj.increase_times = obj.increase_times + 1;
                    elseif (obj.stable_data_average - obj.last_data_average) < -0.001
                        obj.increase_times = 0;
                        obj.decrease_times = obj.decrease_times + 1;
                    else
                        obj.increase_times = 0;
                        obj.decrease_times = 0;
                    end
                    
                    % 检查连续增减是否超过阈值
                    if obj.increase_times >= obj.param_ct_inodc_t || obj.decrease_times >= obj.param_ct_inodc_t
                        obj.jump_flag = 3;
                        obj.stable_data_average = obj.recent_average_buffer(obj.recent_average_buffer_index);
                        obj.recent_average_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
                        obj.recent_average_buffer_index = 1;
                    end
                end
                
                % 更新历史平均值记录
                obj.last_data_average = obj.stable_data_average;
                obj.recent_average_buffer(obj.recent_average_buffer_index) = obj.last_data_average;
                obj.recent_average_buffer_index = obj.recent_average_buffer_index + 1;
                max_recent_len = obj.jump_check_start_data_num + obj.param_ct_inodc_t;
                if obj.recent_average_buffer_index > max_recent_len
                    obj.recent_average_buffer_index = 1;
                end
            end
            
            % 填入本次原始数据到最近数组
            obj.last_data_buffer(obj.last_data_buffer_index) = data;
            obj.last_data_buffer_index = obj.last_data_buffer_index + 1;
            if obj.last_data_buffer_index > obj.param_out_cgm_1_t
                obj.last_data_buffer_index = 1;
            end
            
            if obj.last_data_buffer_filed_num < obj.param_out_cgm_1_t
                obj.last_data_buffer_filed_num = obj.last_data_buffer_filed_num + 1;
            end
            
            jump_flag = obj.jump_flag;
        end
        
        function calculate_variance(obj)
            % 计算方差
            if obj.data_buffer_filed_num < 2
                obj.stable_data_average_variance = 0;
                return;
            end
            
            % 计算当前数据的方差
            variance_sum = 0;
            for i = 1:obj.data_buffer_filed_num
                diff = obj.data_buffer(i) - obj.stable_data_average;
                variance_sum = variance_sum + diff * diff;
            end
            current_variance = variance_sum / obj.data_buffer_filed_num;
            
            % 更新方差缓存（环形缓存）
            obj.data_variance_buffer_sum = obj.data_variance_buffer_sum - ...
                obj.data_variance_buffer(obj.data_variance_buffer_index);
            obj.data_variance_buffer(obj.data_variance_buffer_index) = current_variance;
            obj.data_variance_buffer_sum = obj.data_variance_buffer_sum + current_variance;
            
            obj.data_variance_buffer_index = obj.data_variance_buffer_index + 1;
            if obj.data_variance_buffer_index > obj.DATA_BUF_LEN
                obj.data_variance_buffer_index = 1;
            end
            
            if obj.data_variance_buffer_filed_num < obj.DATA_BUF_LEN
                obj.data_variance_buffer_filed_num = obj.data_variance_buffer_filed_num + 1;
            end
            
            % 计算平均方差并取标准差
            avg_variance = obj.data_variance_buffer_sum / obj.data_variance_buffer_filed_num;
            obj.stable_data_average_variance = sqrt(avg_variance);
        end
        
        function avg = get_data_stable_average(obj)
            % 获取数据稳定均值
            avg = obj.stable_data_average;
        end
        
        function var = get_data_stable_average_variance(obj)
            % 获取数据稳定均方差
            var = obj.stable_data_average_variance;
        end
        
        function num = get_stable_filed_num(obj)
            % 获取稳定数据缓存已经填充的数据个数
            num = obj.data_variance_buffer_filed_num;
        end
        
        function data_buffer_clean(obj)
            % 清空数据缓存
            obj.data_buffer = zeros(1, obj.DATA_BUF_LEN);
            obj.data_buffer_index = 1;
            obj.data_buffer_filed_num = 0;
            obj.data_buffer_sum = 0;
            
            obj.last_data_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
            obj.last_data_buffer_index = 1;
            obj.last_data_buffer_filed_num = 0;
            
            obj.data_variance_buffer = zeros(1, obj.DATA_BUF_LEN);
            obj.data_variance_buffer_index = 1;
            obj.data_variance_buffer_filed_num = 0;
            obj.data_variance_buffer_sum = 0;
            
            obj.stable_data_average = 0;
            obj.stable_data_average_variance = 0;
            obj.recent_average_buffer = zeros(1, obj.LAST_DATA_BUF_LEN);
            obj.recent_average_buffer_index = 1;
            
            obj.out_difference_times = 0;
            obj.last_data_average = 0;
            obj.increase_times = 0;
            obj.decrease_times = 0;
            obj.jump_flag = 0;
        end
    end
end
