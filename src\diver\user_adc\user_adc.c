#include "user_adc.h"

uint16_t adc_channel0_buffer[10];
uint16_t adc_channel6_buffer[10];

//typedef struct
//{
//    uint8_t channel;            //ch57x mcu adc channel : 0 to 13
//    uint16_t buffer_index;      //adc data buffer index, must be initialized to 0  当前 ADC 数据在 buffer 中的索引，用于写入新数据。0 表示 buffer 的起始位置。
//    uint16_t buffer_max_length; //max length of adc buffer  ADC 数据的 buffer 的最大容量（元素数量）。
//    uint16_t * buffer;           //adc buffer to save conver data     指向用于存储 ADC 转换结果的 buffer
//    uint32_t sum;               //sum of adc buffer, must be initialized to 0  ADC buffer 中所有数据的和 (为了计算平均值)
//    uint16_t sum_length;        //sum length of data number, must be initialized to 0   sum 中包含的元素个数，用于计算平均值。 初始化为0
//    float average;              //average of adc buffer     平均值
//    float voltage;              //voltage of adc channel   存储 ADC 转换得到的电压值 (通常通过平均值计算得到)。
//    uint8_t result_ready;    标志位，指示 ADC 数据是否准备好 (0: 未准备好，1: 已准备好)。   
//}adc_channel_table_t;

adc_channel_table_t sys_adc_channel_table[2] = {      //定义一个sys_adc_channel_table的结构体数组，包括两个结构体，结构体类型为adc_channel_table_t
    {
        .average = 0,    //ADC数组的均值
        .buffer = adc_channel0_buffer,  //存放ADC通道10的数组
        .buffer_index = 0,    //ADC的数组数据索引
        .buffer_max_length = sizeof(adc_channel0_buffer)/2,//数组的最大长度
        .channel = 0,
        .sum = 0,
        .sum_length = 0,
        .result_ready = 0,
    },
    {
        .average = 0,    //ADC数组的均值
        .buffer = adc_channel6_buffer,
        .buffer_index = 0,
        .buffer_max_length = sizeof(adc_channel6_buffer)/2,
        .channel = 6,
        .sum = 0,
        .sum_length = 0,
        .result_ready = 0,
    },
};

/**
 * @brief adc 初始化：引脚、采样率、增益
 * 
 */
//当触摸功能联合使用时候,要注意adc的使用必须与触摸采样函数 优先级一致,或者说脚顺序执行,否则对优先级低的操作就要加原子操作,
//避免在高优先级的打断低优先级的ADC 操作导致采样结果混乱甚至死机之风险.
void user_sys_adc_init(void)
{
    GPIOA_ModeCfg(GPIO_Pin_3, GPIO_ModeIN_Floating);  //NTC-ADC采集
    GPIOA_ModeCfg(GPIO_Pin_4, GPIO_ModeIN_Floating);  //BAT-ADC采集
	 //外部信号单通道采样初始化
    ADC_ExtSingleChSampInit(SampleFreq_3_2, ADC_PGA_1_4);   //3.2M采样率，-12dB(1/4 倍)
	//CH579M其参考电压Vref是1.05v(半量程).默认的电压测量范围是0-2.1v
	//芯片ADC的前级提供了一个PGA,可以通过调整其放大倍数,来实现不同的量程.
	//PGA增益	      采样值到电压换算	        理论可测电压上限	            理论可测电压范围(Vref=1.05V+±0.015V)	  建议实际测量电压范围
  //-12dB(1/4倍)	(ADC/512-3)*Vref	             5*Vref	                       -0.2V ～ VIO33+0.2V                  	2.9V ～ VIO33（3.3V）
  //-6dB(1/2倍)	(ADC/1024-1)*Vref	             3*Vref	                       -0.2V ～ 3.15V	                          1.9V ～ 3V
  //0db(1倍)	    (ADC/2048)*Vref	               2*Vref	                          0V ～ 2.1V	                           0V ～ 2V
  //6db(2倍)	    (ADC/4096+0.5)*Vref	           1.5*Vref	                        0.525V ～ 1.575V	                   0.6V ～ 1.5V
}

/**
 * @brief 非阻塞式的扫描adc预设通道
 * @param channel_table adc通道表
 * @param channel_num 通道表内的通道数量
 */ //adc_channel_table_t * channel_table: 第一个参数是指向 adc_channel_table_t 类型的指针
//  使用时adc_scan_channel_task(sys_adc_channel_table, sizeof(sys_adc_channel_table)/sizeof(adc_channel_table_t)); 就是一个数组结构体，两张表
void adc_scan_channel_task(adc_channel_table_t * channel_table, uint8_t channel_num)
{
    static uint8_t table_index = 0;
	  if(channel_num == 0) return;    //需要转换的通道数为0，则直接返回
    
    static uint8_t conver_ing = 0; //标志位，指示当前ADC转换是否正在进行。conver_ing = 0表示未进行转换。 conver_ing = 1表示正在进行转换。
	if(!conver_ing){//如果标志位为1（正在转换），那么if后为假，调至else if
        ADC_ChannelCfg(channel_table[table_index].channel); //配置采样通道
        R8_ADC_CONVERT = RB_ADC_START; //开启转换
// R8_ADC_CONVERT      (*((PUINT8V)0x4000105A))  // RW, ADC convert control
//#define  RB_ADC_START       0x01                      // RW, ADC convert start control: 0=stop ADC convert, 1=start an ADC convert, auto clear
		conver_ing = 1;      //表示正在转换
    }
	//检查 ADC 转换是否仍在进行（判断 RB_ADC_START 位是否仍然是 1）。 如果转换完成， RB_ADC_START 位会变成 0。
    else if(!(R8_ADC_CONVERT & RB_ADC_START)){//表示转换完成
        uint16_t * data_buffer = channel_table[table_index].buffer;  //获取当前通道的数据缓存区的指针. 即数据数据
        uint16_t data_index = channel_table[table_index].buffer_index; // 获取当前通道的缓冲区的写入索引(缓冲区的下标)。

        channel_table[table_index].sum -= data_buffer[data_index];   //从 sum 中减去 data_index 对应的旧数据.
			data_buffer[data_index] = R16_ADC_DATA&RB_ADC_DATA;  //16位寄存器，转换结果为低12位，进行位掩码（0x0FFF）
		  //R16_ADC_DATA        (*((PUINT16V)0x4000105C)) // RO, ADC data
			//RB_ADC_DATA        0x0FFF                    // RO, ADC conversion data
			channel_table[table_index].sum += data_buffer[data_index]; //每次采集到数据后更新数组（减去原来旧的数据并加上新采集的数据）

        if(channel_table[table_index].sum_length < channel_table[table_index].buffer_max_length){
            channel_table[table_index].sum_length ++;
        }
				//转换通道的数组数据长度必须大于小于设定的长度
				
        channel_table[table_index].average = (channel_table[table_index].sum * 1.0f) / channel_table[table_index].sum_length;    
				//平均值 = SUM /个数
				
        channel_table[table_index].voltage =  (channel_table[table_index].average / 512 - 3) * 1.05f;   
        //依据手册的公式：(ADC/512-3)*Vref，Vref：内部模拟电路的电源节点 VINTA 的实际电压值，通常为 1.05V±0.02V。 				

        channel_table[table_index].buffer_index ++;  //将缓冲区索引 buffer_index 递增，指向下一个存储位置。
        channel_table[table_index].buffer_index %= channel_table[table_index].buffer_max_length;//使用模运算，将 buffer_index 限制在缓冲区的大小范围内，形成环形缓冲区。

        conver_ing = 0;  //表示当前通道的转换已完成
        if(channel_table[table_index].buffer_index == 0){  //如果当前channel通道的数据已经满了
            table_index ++;   //切换到下一个通道
            table_index %= channel_num;      //限制table_index的值在channel_num的范围之内
        }   
        
        channel_table[table_index].result_ready = (channel_table[table_index].sum_length == channel_table[table_index].buffer_max_length);
				//设置 result_ready 标志位，如果 sum_length 等于 buffer_max_length，说明缓冲区已经填满，数据就绪。
		}
}


